from flask import Flask, render_template, request, jsonify, redirect, url_for, session, send_file
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, AdaBoostClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
import pickle
import os
import seaborn as sns
import matplotlib.pyplot as plt
import plotly.express as px
import plotly.graph_objects as go
import io
import base64
from matplotlib.figure import Figure
from openai import OpenAI
import json
import shap
import warnings
warnings.filterwarnings('ignore')

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# DeepSeek API Configuration
client = OpenAI(
    base_url="https://integrate.api.nvidia.com/v1",
    api_key="**********************************************************************"
)

def generate_health_advice(data, predictions, lang='en'):
    """Generate personalized health advice using DeepSeek API"""
    # Combine predictions from all models
    majority_prediction = 1 if sum(pred[0] for pred in predictions.values()) >= len(predictions)/2 else 0

    # Define concise prompts based on risk level
    if lang == 'en':
        prompt = f"""Patient data:
- Age: {data['age']}
- Gender: {data['gender']}
- BMI: {data['bmi']}
- HbA1c: {data['HbA1c_level']}%
- Blood Glucose: {data['blood_glucose_level']} mg/dL (Normal: <140, Prediabetes: 140–199, Diabetes: ≥200)
- Hypertension: {'Yes' if int(data['hypertension']) == 1 else 'No'}
- Heart Disease: {'Yes' if int(data['heart_disease']) == 1 else 'No'}
- Smoking History: {'Current' if data['smoking_history'].lower() == 'current' else 'Former' if data['smoking_history'].lower() == 'former' else 'Never' if data['smoking_history'].lower() == 'never' else 'Ever' if data['smoking_history'].lower() == 'ever' else 'Not Current' if data['smoking_history'].lower() == 'not current' else 'No Info'}
- Risk Level: {'High risk' if majority_prediction == 1 else 'Lower risk'} of diabetes"""
        system_message = "You are a concise medical assistant specializing in diabetes and chronic conditions. Based on the patient's provided information (age, gender, BMI, HbA1c, blood glucose, hypertension, heart disease, smoking status), generate exactly five personalized medical advice sentences, each starting with 'The medical advice is:'. For critical conditions (e.g., HbA1c ≥9% or glucose ≥300 mg/dL, severe hypertension, or critical cardiac issues), include urgent advice to consult a doctor immediately. For prediabetes (HbA1c 5.7–6.4% or glucose 140–199 mg/dL), recommend specific prevention steps (e.g., reduce sugar intake) and consulting a doctor within a week. For diabetes (HbA1c ≥6.5%), advise daily glucose monitoring and dietary changes. For obesity (BMI ≥30), specify a weight loss goal (5-10% of body weight), calorie reduction (500-1000 kcal/day), and exercise (30 min/day). For overweight (BMI 25–29.9), recommend moderate weight loss (3-5%) and exercise (20-30 min/day). For hypertension, advise reducing salt to <2g/day and weekly blood pressure monitoring. For heart disease, recommend a low-saturated-fat diet and cardiac check-ups every 6 months. If the patient is a current smoker, urge quitting smoking and seeking medical support. If a former smoker, encourage continued abstinence and periodic lung/heart screenings. If never smoked, acknowledge as a positive factor. Ensure all advice is practical, actionable, and tailored to the patient's data, avoiding generic phrases like 'maintain a healthy lifestyle'. Do not include any extra explanations or text outside the five advice sentences."
    else:
        prompt = f"""Данные пациента:
- Возраст: {data['age']}
- Пол: {'Женский' if data['gender'] == 'Female' else 'Мужской' if data['gender'] == 'Male' else 'Неизвестно'}
- ИМТ: {data['bmi']}
- HbA1c: {data['HbA1c_level']}%
- Глюкоза: {data['blood_glucose_level']} мг/дл (Норма: <140, Преддиабет: 140–199, Диабет: ≥200)
- Гипертония: {'Да' if int(data['hypertension']) == 1 else 'Нет'}
- Сердечные заболевания: {'Да' if int(data['heart_disease']) == 1 else 'Нет'}
- История курения: {'Текущий курильщик' if data['smoking_history'].lower() == 'current' else 'Бывший курильщик' if data['smoking_history'].lower() == 'former' else 'Никогда не курил' if data['smoking_history'].lower() == 'never' else 'Курил ранее' if data['smoking_history'].lower() == 'ever' else 'Не курит сейчас' if data['smoking_history'].lower() == 'not current' else 'Нет информации'}
- Уровень риска: {'Высокий риск' if majority_prediction == 1 else 'Низкий риск'} диабета"""
        system_message = "detailed thinking off. Вы лаконичный медицинский ассистент, специализирующийся на диабете и хронических заболеваниях. На основе предоставленных данных пациента (возраст, пол, ИМТ, HbA1c, уровень глюкозы в крови, гипертония, сердечные заболевания, статус курения) сгенерируйте ровно пять персонализированных медицинских рекомендаций, каждая из которых начинается с «Медицинская рекомендация:». Для критических состояний (например, HbA1c ≥9% или глюкоза ≥300 мг/дл, тяжелая гипертония или критические сердечные проблемы) включите срочный совет немедленно обратиться к врачу. Для предиабета (HbA1c 5.7–6.4% или глюкоза 140–199 мг/дл) рекомендуйте конкретные меры профилактики (например, снижение потребления сахара) и консультацию с врачом в течение недели. Для диабета (HbA1c ≥6.5%) советуйте ежедневный мониторинг глюкозы и изменения в питании. Для ожирения (ИМТ ≥30) укажите цель снижения веса (5–10% массы тела), сокращение калорий (500–1000 ккал/день) и физические упражнения (30 мин/день). Для избыточного веса (ИМТ 25–29.9) рекомендуйте умеренное снижение веса (3–5%) и упражнения (20–30 мин/день). Для гипертонии советуйте сократить потребление соли до <2 г/день и еженедельный мониторинг артериального давления. Для сердечных заболеваний рекомендуйте диету с низким содержанием насыщенных жиров и кардиологические осмотры каждые 6 месяцев. Если пациент курит в настоящее время, настоятельно советуйте бросить курить и обратиться за медицинской поддержкой. Если пациент бывший курильщик, поощряйте продолжение воздержания и периодические обследования легких/сердца. Если пациент никогда не курил, отметьте это как положительный фактор. Убедитесь, что все рекомендации практичны, конкретны и адаптированы к данным пациента, избегая общих фраз, таких как «вести здоровый образ жизни». Не включайте никаких дополнительных объяснений или текста, кроме пяти рекомендательных предложений.."

    try:
        completion = client.chat.completions.create(
            model="nvidia/llama-3.3-nemotron-super-49b-v1",
            messages=[{
                "role": "system",
                "content": system_message
            },
            {
                "role": "user",
                "content": prompt
            }],
            temperature=0.1,
            top_p=0.7,
            max_tokens=500,
            stream=False
        )

        advice = completion.choices[0].message.content.strip()
        return advice.rstrip('.')

    except Exception as e:
        print(f"Error generating health advice: {str(e)}")
        return "Consult your doctor for personalized advice." if lang == 'en' else "Проконсультируйтесь с врачом для получения персональных рекомендаций."

# Global variables for models
models = {
    'random_forest': None,
    'adaboost': None,
    'decision_tree': None,
    'logistic_regression': None,
    'svm': None
}

# Model file paths with a more reliable directory structure
MODEL_PATHS = {
    'random_forest': os.path.join('models', 'SR_Random_Forest.pkl'),
    'adaboost': os.path.join('models', 'SR_AdaBoost.pkl'),
    'decision_tree': os.path.join('models', 'SR_Decision_Tree.pkl'),
    'logistic_regression': os.path.join('models', 'SR_Logistic_Regression.pkl'),
    'svm': os.path.join('models', 'svm_linear_pickle.pkl')
}

# Expected features for the models (14 features)
EXPECTED_FEATURES = [
    'age', 'hypertension', 'heart_disease', 'bmi', 'HbA1c_level', 'blood_glucose_level',
    'gender_Female', 'gender_Male', 'smoking_history_No Info', 'smoking_history_current',
    'smoking_history_ever', 'smoking_history_former', 'smoking_history_never',
    'smoking_history_not current'
]

# Human-readable feature names for display
FEATURE_DISPLAY_NAMES = {
    'age': {'en': 'Age', 'ru': 'Возраст'},
    'hypertension': {'en': 'Hypertension', 'ru': 'Гипертония'},
    'heart_disease': {'en': 'Heart Disease', 'ru': 'Сердечные заболевания'},
    'bmi': {'en': 'BMI', 'ru': 'ИМТ'},
    'HbA1c_level': {'en': 'HbA1c Level', 'ru': 'Уровень HbA1c'},
    'blood_glucose_level': {'en': 'Blood Glucose', 'ru': 'Уровень глюкозы'},
    'gender_Female': {'en': 'Female Gender', 'ru': 'Женский пол'},
    'gender_Male': {'en': 'Male Gender', 'ru': 'Мужской пол'},
    'smoking_history_No Info': {'en': 'No Smoking Info', 'ru': 'Нет данных о курении'},
    'smoking_history_current': {'en': 'Current Smoker', 'ru': 'Курит сейчас'},
    'smoking_history_ever': {'en': 'Ever Smoked', 'ru': 'Курил когда-либо'},
    'smoking_history_former': {'en': 'Former Smoker', 'ru': 'Бывший курильщик'},
    'smoking_history_never': {'en': 'Never Smoked', 'ru': 'Никогда не курил'},
    'smoking_history_not current': {'en': 'Not Current Smoker', 'ru': 'Не курит сейчас'}
}

# Error messages in both languages
ERROR_MESSAGES = {
    'en': {
        'model_not_loaded': 'Model is not loaded correctly',
        'model_not_selected': 'Please select a model',
        'all_fields_required': 'All fields are required',
        'invalid_numeric': 'Please enter valid numeric values',
        'age_range': 'Age must be between 0 and 120',
        'bmi_range': 'BMI must be between 15 and 75',
        'hba1c_range': 'HbA1c level must be between 3 and 20',
        'svm_feature_error': 'One or more input values are outside the expected range for the SVM model.',
        'svm_convergence_warning': 'The SVM model had difficulty making a confident prediction with the given input values.'
    },
    'ru': {
        'model_not_loaded': 'Модель не загружена корректно',
        'model_not_selected': 'Пожалуйста, выберите модель',
        'all_fields_required': 'Все поля обязательны для заполнения',
        'invalid_numeric': 'Пожалуйста, введите корректные числовые значения',
        'age_range': 'Возраст должен быть от 0 до 120',
        'bmi_range': 'ИМТ должен быть от 15 до 75',
        'hba1c_range': 'Уровень HbA1c должен быть от 3 до 20',
        'glucose_range': 'Уровень глюкозы в крови должен быть от 50 до 400',
        'processing_error': 'Произошла ошибка при обработке данных',
        'svm_prediction_error': 'Модель SVM столкнулась с ошибкой при обработке ваших входных значений. Это может быть связано с неожиданными диапазонами значений.',
        'svm_feature_error': 'Одно или несколько входных значений находятся за пределами ожидаемого диапазона для модели SVM.',
        'svm_convergence_warning': 'Модель SVM испытывала трудности с уверенным прогнозом при заданных входных значениях.'
    }
}

def load_models():
    """Load all models with enhanced error handling and debugging"""
    models_loaded = {model_name: False for model_name in models.keys()}
    models_status = []

    # Get absolute path to the models directory
    base_dir = os.path.dirname(os.path.abspath(__file__))

    for model_name, rel_path in MODEL_PATHS.items():
        abs_path = os.path.join(base_dir, rel_path)
        try:
            # Verify file exists
            if not os.path.exists(abs_path):
                models_status.append(f"Model file not found: {abs_path}")
                continue

            # Check if file is readable
            if not os.access(abs_path, os.R_OK):
                models_status.append(f"Model file not readable: {abs_path}")
                continue

            # Load and verify the model
            with open(abs_path, 'rb') as file:
                try:
                    model = pickle.load(file)
                    if not hasattr(model, 'predict'):
                        models_status.append(f"Invalid model format: {model_name} does not have predict method")
                        continue

                    models[model_name] = model
                    models_loaded[model_name] = True
                    models_status.append(f"{model_name} model loaded successfully!")
                except (pickle.UnpicklingError, EOFError) as e:
                    models_status.append(f"Error unpickling {model_name} model: {str(e)}")
                    continue

        except Exception as e:
            models_status.append(f"Error loading {model_name} model: {str(e)}")
            models[model_name] = None

    # Print detailed status report
    print("\nModel Loading Status Report:")
    print("=" * 50)
    for status in models_status:
        print(status)
    print("=" * 50)

    if not any(models_loaded.values()):
        print("\nCRITICAL: No models were loaded successfully!")
        print("Checked paths:")
        for model_name, rel_path in MODEL_PATHS.items():
            print(f"- {os.path.join(base_dir, rel_path)}")
        return False

    print(f"\nSuccessfully loaded {sum(models_loaded.values())}/{len(models_loaded)} models")
    return True

def calculate_shap_feature_importance(model_type, features, lang='en', top_n=6):
    """Calculate SHAP-based feature importance for the given model and features"""
    try:
        if model_type not in models or models[model_type] is None:
            return []

        model = models[model_type]
        features_array = np.array(features).reshape(1, -1)

        # Calculate SHAP values based on model type
        if model_type in ['random_forest', 'adaboost', 'decision_tree']:
            # For tree-based models, use TreeExplainer
            explainer = shap.TreeExplainer(model)
            shap_values = explainer.shap_values(features_array)

            # For binary classification, shap_values might be a list
            if isinstance(shap_values, list):
                shap_values = shap_values[1]  # Use positive class SHAP values

            # Get SHAP values for the single prediction
            if len(shap_values.shape) > 1:
                shap_values = shap_values[0]

        elif model_type in ['logistic_regression', 'svm']:
            # For linear models, use coefficients multiplied by feature values
            if hasattr(model, 'coef_'):
                shap_values = model.coef_[0] * features_array[0]
            else:
                # Fallback to basic feature importance
                return []
        else:
            return []

        # Create feature importance data
        feature_importance_data = []
        for i, feature_name in enumerate(EXPECTED_FEATURES):
            importance_value = float(shap_values[i])

            # Skip features with zero or very small importance
            if abs(importance_value) < 0.001:
                continue

            feature_importance_data.append({
                'feature': feature_name,
                'importance': importance_value,
                'abs_importance': abs(importance_value),
                'display_name': FEATURE_DISPLAY_NAMES.get(feature_name, {}).get(lang, feature_name),
                'direction': 'positive' if importance_value > 0 else 'negative'
            })

        # Sort by absolute importance and get top N
        feature_importance_data.sort(key=lambda x: x['abs_importance'], reverse=True)
        top_features = feature_importance_data[:top_n]

        # Calculate percentages based on the sum of absolute values of top features
        total_abs_importance = sum(item['abs_importance'] for item in top_features)

        if total_abs_importance > 0:
            for item in top_features:
                item['percentage'] = round((item['abs_importance'] / total_abs_importance) * 100, 1)
        else:
            for item in top_features:
                item['percentage'] = 0

        return top_features

    except Exception as e:
        print(f"Error calculating SHAP feature importance: {str(e)}")
        return []

# Load models when the app starts
if not load_models():
    print("Failed to load ANY models! Application cannot function without at least one model.")
    exit(1)
else:
    print("Application starting with available models...")

def create_feature_importance_plot(model_type):
    """Create a feature importance plot using seaborn"""
    if model_type not in models or models[model_type] is None:
        return None

    model = models[model_type]

    # Get feature importance based on model type
    if model_type in ['logistic_regression', 'svm']:
        # For Logistic Regression and SVM, use absolute values of coefficients
        importance_values = np.abs(model.coef_[0])
    else:
        # For tree-based models, use feature_importances_
        importance_values = model.feature_importances_

    feature_importance = pd.DataFrame({
        'feature': EXPECTED_FEATURES,
        'importance': importance_values
    }).sort_values('importance', ascending=True)

    # Create matplotlib figure with larger size
    plt.figure(figsize=(12, 8))

    # Create barplot with improved styling
    ax = sns.barplot(data=feature_importance, y='feature', x='importance', palette='magma')

    # Customize the plot
    plt.title(f'Feature Importance - {model_type.replace("_", " ").title()}', pad=20, fontsize=14)
    plt.xlabel('Importance', fontsize=12)
    plt.ylabel('Feature', fontsize=12)

    # Add value labels to the bars
    for i, v in enumerate(feature_importance['importance']):
        ax.text(v, i, f'{v:.3f}', va='center', fontsize=10)

    # Adjust layout to prevent label cutoff
    plt.tight_layout()

    # Save plot to bytes buffer
    buf = io.BytesIO()
    plt.savefig(buf, format='png', bbox_inches='tight', dpi=300)
    plt.close()
    buf.seek(0)
    return buf

def create_feature_contribution_plot(model_type, features):
    """Create a feature contribution plot using seaborn"""
    if model_type not in models or models[model_type] is None:
        return None

    model = models[model_type]
    feature_values = pd.DataFrame([features], columns=EXPECTED_FEATURES)

    # Get contribution based on model type
    if model_type in ['logistic_regression', 'svm']:
        # For Logistic Regression and SVM, multiply coefficients by feature values
        contributions = model.coef_[0] * features
    else:
        # For tree-based models, use feature importances multiplied by feature values
        contributions = model.feature_importances_ * features

    contribution_df = pd.DataFrame({
        'feature': EXPECTED_FEATURES,
        'contribution': contributions
    }).sort_values('contribution', ascending=True)

    # Create matplotlib figure with larger size
    plt.figure(figsize=(12, 8))

    # Create barplot with improved styling
    colors = ['#d73027' if x < 0 else '#4575b4' for x in contribution_df['contribution']]
    ax = sns.barplot(data=contribution_df, y='feature', x='contribution', palette=colors)

    # Customize the plot
    plt.title(f'Feature Contribution to Prediction - {model_type.replace("_", " ").title()}', pad=20, fontsize=14)
    plt.xlabel('Contribution to Prediction', fontsize=12)
    plt.ylabel('Feature', fontsize=12)

    # Add value labels to the bars
    for i, v in enumerate(contribution_df['contribution']):
        ax.text(v, i, f'{v:.3f}', va='center', fontsize=10)

    # Add a vertical line at x=0
    plt.axvline(x=0, color='black', linestyle='-', linewidth=0.5)

    # Adjust layout to prevent label cutoff
    plt.tight_layout()

    # Save plot to bytes buffer
    buf = io.BytesIO()
    plt.savefig(buf, format='png', bbox_inches='tight', dpi=300)
    plt.close()
    buf.seek(0)
    return buf

@app.route('/', methods=['GET'])
def home():
    if 'patient_name' not in session:
        return redirect(url_for('login'))
    return render_template('index.html', patient_name=session['patient_name'])

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        patient_name = request.form.get('patient_name')
        if patient_name:
            session['patient_name'] = patient_name
            return redirect(url_for('home'))
    return render_template('login.html')

@app.route('/predict', methods=['POST'])
def predict():
    try:
        # Get the selected model
        model_type = request.form.get('model_type', 'random_forest')
        if model_type not in models:
            return jsonify({
                'error': ERROR_MESSAGES[request.form.get('lang', 'en')]['model_not_selected'],
                'risk_level': 'Error'
            })

        model = models[model_type]
        if model is None:
            return jsonify({
                'error': ERROR_MESSAGES[request.form.get('lang', 'en')]['model_not_loaded'],
                'risk_level': 'Error'
            })

        lang = request.form.get('lang', 'en')
        data = {
            'age': request.form.get('age'),
            'gender': request.form.get('gender'),
            'hypertension': request.form.get('hypertension'),
            'heart_disease': request.form.get('heart_disease'),
            'smoking_history': request.form.get('smoking_history'),
            'bmi': request.form.get('bmi'),
            'HbA1c_level': request.form.get('HbA1c_level'),
            'blood_glucose_level': request.form.get('blood_glucose_level')
        }

        # Validate inputs
        if not all(value is not None and value != '' for value in data.values()):
            return jsonify({'error': ERROR_MESSAGES[lang]['all_fields_required'], 'risk_level': 'Error'})

        try:
            # Convert and validate numeric inputs with additional error handling
            age = float(data['age'])
            bmi = float(data['bmi'])
            HbA1c_level = float(data['HbA1c_level'])
            blood_glucose_level = float(data['blood_glucose_level'])
            hypertension = int(data['hypertension'])
            heart_disease = int(data['heart_disease'])

            if not (0 <= age <= 120):
                return jsonify({'error': ERROR_MESSAGES[lang]['age_range'], 'risk_level': 'Error'})
            if not (15 <= bmi <= 75):
                return jsonify({'error': ERROR_MESSAGES[lang]['bmi_range'], 'risk_level': 'Error'})
            if not (3 <= HbA1c_level <= 20):
                return jsonify({'error': ERROR_MESSAGES[lang]['hba1c_range'], 'risk_level': 'Error'})
            if not (50 <= blood_glucose_level <= 530):
                return jsonify({'error': ERROR_MESSAGES[lang]['glucose_range'], 'risk_level': 'Error'})
        except ValueError:
            return jsonify({'error': ERROR_MESSAGES[lang]['invalid_numeric'], 'risk_level': 'Error'})

        # Process categorical variables
        gender_Female = 1 if data['gender'] == 'Female' else 0
        gender_Male = 1 if data['gender'] == 'Male' else 0

        smoking_history = data['smoking_history'].lower()
        smoking_history_No_Info = 1 if smoking_history == 'no info' else 0
        smoking_history_current = 1 if smoking_history == 'current' else 0
        smoking_history_ever = 1 if smoking_history == 'ever' else 0
        smoking_history_former = 1 if smoking_history == 'former' else 0
        smoking_history_never = 1 if smoking_history == 'never' else 0
        smoking_history_not_current = 1 if smoking_history == 'not current' else 0

        # Create feature array ensuring correct data type and order
        features = np.array([
            [
                age, hypertension, heart_disease, bmi, HbA1c_level, blood_glucose_level,
                gender_Female, gender_Male, smoking_history_No_Info, smoking_history_current,
                smoking_history_ever, smoking_history_former, smoking_history_never,
                smoking_history_not_current
            ]
        ])

        # Get predictions from all models
        all_predictions = {}
        for model_name, model_instance in models.items():
            if model_instance is not None:
                try:
                    prediction = model_instance.predict(features)
                    all_predictions[model_name] = prediction
                except Exception as e:
                    print(f"Error with {model_name} prediction: {str(e)}")

        # Get prediction and probability for the selected model
        try:
            if model_type == 'svm':
                # For SVM, get binary prediction and assign probability as 1.0 or 0.0
                prediction = model.predict(features)
                # Assign probability as 1.0 if prediction == 1 (positive class), else 0.0
                proba = 1.0 if prediction[0] == 1 else 0.0
            else:
                # For all other models, use predict_proba
                prediction = model.predict(features)
                proba = model.predict_proba(features)[0][1]

            if proba < 0.3:
                risk_level = 'Low'
            elif proba < 0.6:
                risk_level = 'Medium'
            else:
                risk_level = 'High'

            # Only generate health advice if user has given consent
            ai_advice_consent = request.form.get('ai_advice_consent') == 'true'
            health_advice = generate_health_advice(data, all_predictions, lang) if ai_advice_consent else None

            # Calculate SHAP-based feature importance
            feature_importance = calculate_shap_feature_importance(
                model_type,
                features[0],  # Pass the feature array
                lang=lang,
                top_n=6
            )

            response_data = {
                'prediction': int(prediction[0]),
                'probability': float(proba),
                'risk_level': risk_level,
                'model_type': model_type,
                'feature_importance': feature_importance
            }

            # Only include health advice in response if consent was given
            if health_advice:
                response_data['health_advice'] = health_advice

            return jsonify(response_data)

        except Exception as model_error:
            print(f"Model prediction error: {str(model_error)}")
            return jsonify({
                'error': ERROR_MESSAGES[lang]['processing_error'],
                'risk_level': 'Error'
            })

    except Exception as e:
        print(f"Error during prediction: {str(e)}")
        return jsonify({
            'error': ERROR_MESSAGES[request.form.get('lang', 'en')]['processing_error'],
            'risk_level': 'Error'
        })

@app.route('/feature_importance/<model_type>')
def get_feature_importance(model_type):
    """Return a static feature importance plot as PNG"""
    if (model_type not in models or models[model_type] is None):
        return 'Model not found', 404

    plot_buffer = create_feature_importance_plot(model_type)
    if plot_buffer is None:
        return 'Could not create plot', 500

    return send_file(
        plot_buffer,
        mimetype='image/png',
        as_attachment=False,
        download_name=f'feature_importance_{model_type}.png'
    )

@app.route('/feature_contribution/<model_type>', methods=['POST'])
def get_feature_contribution(model_type):
    """Return a feature contribution plot for the current prediction"""
    if model_type not in models or models[model_type] is None:
        return 'Model not found', 404

    # Get feature values from the request
    try:
        features = request.get_json()
        if not features or 'features' not in features:
            return 'No feature data provided', 400

        feature_array = np.array(features['features'])
        plot_buffer = create_feature_contribution_plot(model_type, feature_array)

        if plot_buffer is None:
            return 'Could not create plot', 500

        return send_file(
            plot_buffer,
            mimetype='image/png',
            as_attachment=False,
            download_name=f'feature_contribution_{model_type}.png'
        )
    except Exception as e:
        print(f"Error creating feature contribution plot: {str(e)}")
        return 'Error creating plot', 500

if __name__ == '__main__':
    app.run(debug=True)
